using UnityEngine;
using BTR.EnemySystem.Entities;
using BTR.EnemySystem.Strategies.Combat;
using BTR.EnemySystem.Strategies.Movement;

namespace BTR.EnemySystem.Entities
{
    /// <summary>
    /// Enhanced enemy entity that demonstrates the new strategy pattern.
    /// This entity can use different combat and movement strategies dynamically.
    /// </summary>
    public class StrategicEnemyEntity : CombatEntity
    {
        [Header("Strategic Enemy Settings")]
        [SerializeField] private string enemyType = "Strategic";
        [SerializeField] private bool autoFindStrategies = true;

        [Header("Strategy References")]
        [SerializeField] private CombatStrategy combatStrategy;
        [SerializeField] private MovementStrategy movementStrategy;

        // Strategy management
        private CombatStrategy[] availableCombatStrategies;
        private MovementStrategy[] availableMovementStrategies;
        private int currentCombatStrategyIndex = 0;
        private int currentMovementStrategyIndex = 0;

        // Target management
        private Transform currentTarget;

        // Events
        public event System.Action<CombatStrategy> OnCombatStrategyChanged;
        public event System.Action<MovementStrategy> OnMovementStrategyChanged;

        protected override void PerformCombatInitialization()
        {
            // Find available strategies
            if (autoFindStrategies)
            {
                FindAvailableStrategies();
            }

            // Initialize current strategies
            InitializeStrategies();

            // Register with migration tracker


            if (enableDebugLogs)
            {
                Debug.Log($"[StrategicEnemyEntity] {enemyType} enemy initialized with {availableCombatStrategies?.Length ?? 0} combat strategies and {availableMovementStrategies?.Length ?? 0} movement strategies");
            }
        }

        protected override void PerformCombatActivation()
        {
            // Activate current strategies
            ActivateStrategies();

            // Find and set target
            FindAndSetTarget();

            if (enableDebugLogs)
            {
                Debug.Log($"[StrategicEnemyEntity] {enemyType} enemy activated");
            }
        }

        protected override void PerformCombatDeactivation()
        {
            // Deactivate strategies
            DeactivateStrategies();

            if (enableDebugLogs)
            {
                Debug.Log($"[StrategicEnemyEntity] {enemyType} enemy deactivated");
            }
        }

        protected override void PerformCombatCleanup()
        {
            // Cleanup strategies
            CleanupStrategies();

            // Clear events
            OnCombatStrategyChanged = null;
            OnMovementStrategyChanged = null;

            // Clear arrays to prevent serialization issues
            availableCombatStrategies = null;
            availableMovementStrategies = null;
            combatStrategy = null;
            movementStrategy = null;
            currentTarget = null;

            // Update migration status


            if (enableDebugLogs)
            {
                Debug.Log($"[StrategicEnemyEntity] {enemyType} enemy cleaned up");
            }
        }

        protected override void HandleDamageReceived(float damage, float oldHealth, float newHealth)
        {
            // Handle damage effects
            if (enableDebugLogs)
            {
                Debug.Log($"[StrategicEnemyEntity] {enemyType} enemy took {damage} damage - Health: {oldHealth} -> {newHealth}");
            }

            // Could trigger strategy changes based on health
            CheckForStrategyChanges(newHealth / MaxHealth);
        }

        protected override void HandleHealing(float healing, float oldHealth, float newHealth)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[StrategicEnemyEntity] {enemyType} enemy healed {healing} - Health: {oldHealth} -> {newHealth}");
            }
        }

        protected override void HandleVulnerabilityChanged(bool isVulnerable)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[StrategicEnemyEntity] {enemyType} enemy vulnerability changed to: {isVulnerable}");
            }
        }

        protected override void PerformDeathHandling()
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[StrategicEnemyEntity] {enemyType} enemy died - ID: {EntityID}");
            }

            // Deactivate strategies before death
            DeactivateStrategies();

            // Handle death effects
            Invoke(nameof(DeactivateAfterDeath), 1f);
        }

        private void FindAvailableStrategies()
        {
            // Find all combat strategies on this GameObject
            availableCombatStrategies = GetComponents<CombatStrategy>();
            availableMovementStrategies = GetComponents<MovementStrategy>();

            // Set current strategies if not already set
            if (combatStrategy == null && availableCombatStrategies.Length > 0)
            {
                combatStrategy = availableCombatStrategies[0];
            }

            if (movementStrategy == null && availableMovementStrategies.Length > 0)
            {
                movementStrategy = availableMovementStrategies[0];
            }
        }

        private void InitializeStrategies()
        {
            // Configure strategies with entity configuration
            ConfigureStrategies();

            // Initialize combat strategy
            if (combatStrategy != null && !combatStrategy.IsInitialized)
            {
                combatStrategy.Initialize();
            }

            // Initialize movement strategy
            if (movementStrategy != null && !movementStrategy.IsInitialized)
            {
                movementStrategy.Initialize();
            }
        }

        private void ConfigureStrategies()
        {
            if (configuration != null)
            {
                // Configure combat strategy if it supports configuration
                if (combatStrategy is ProjectileCombatStrategy projectileStrategy)
                {
                    projectileStrategy.SetConfiguration(configuration);

                    if (enableDebugLogs)
                    {
                        Debug.Log($"[StrategicEnemyEntity] Applied configuration to ProjectileCombatStrategy - Homing: {configuration.enableHoming}");
                    }
                }

                // Add configuration for other strategy types as needed
                // if (movementStrategy is SomeMovementStrategy someStrategy)
                // {
                //     someStrategy.SetConfiguration(configuration);
                // }
            }
            else if (enableDebugLogs)
            {
                Debug.LogWarning($"[StrategicEnemyEntity] No configuration available to pass to strategies on {gameObject.name}");
            }
        }

        private void ActivateStrategies()
        {
            // Activate combat strategy
            if (combatStrategy != null && !combatStrategy.IsActive)
            {
                combatStrategy.Activate();
            }

            // Activate movement strategy
            if (movementStrategy != null && !movementStrategy.IsActive)
            {
                movementStrategy.Activate();
            }
        }

        private void DeactivateStrategies()
        {
            // Deactivate combat strategy
            if (combatStrategy != null && combatStrategy.IsActive)
            {
                combatStrategy.Deactivate();
            }

            // Deactivate movement strategy
            if (movementStrategy != null && movementStrategy.IsActive)
            {
                movementStrategy.Deactivate();
            }
        }

        private void CleanupStrategies()
        {
            // Cleanup combat strategy
            if (combatStrategy != null)
            {
                combatStrategy.Cleanup();
            }

            // Cleanup movement strategy
            if (movementStrategy != null)
            {
                movementStrategy.Cleanup();
            }
        }

        private void FindAndSetTarget()
        {
            // Find player target
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                SetTarget(player.transform);
            }
        }

        public void SetTarget(Transform target)
        {
            currentTarget = target;

            // Set target for strategies
            if (combatStrategy != null)
            {
                combatStrategy.SetTarget(target);
            }

            if (movementStrategy != null)
            {
                movementStrategy.SetTarget(target);
            }

            if (enableDebugLogs)
            {
                Debug.Log($"[StrategicEnemyEntity] Target set to: {target?.name ?? "null"}");
            }
        }

        public void SetCombatStrategy(CombatStrategy newStrategy)
        {
            if (newStrategy == combatStrategy)
                return;

            // Deactivate old strategy
            if (combatStrategy != null && combatStrategy.IsActive)
            {
                combatStrategy.Deactivate();
            }

            // Set new strategy
            combatStrategy = newStrategy;

            // Configure, initialize and activate new strategy
            if (combatStrategy != null)
            {
                // Configure the strategy if it supports configuration
                if (configuration != null && combatStrategy is ProjectileCombatStrategy projectileStrategy)
                {
                    projectileStrategy.SetConfiguration(configuration);
                }

                if (!combatStrategy.IsInitialized)
                {
                    combatStrategy.Initialize();
                }

                if (IsActive && !combatStrategy.IsActive)
                {
                    combatStrategy.Activate();
                    combatStrategy.SetTarget(currentTarget);
                }
            }

            OnCombatStrategyChanged?.Invoke(combatStrategy);

            if (enableDebugLogs)
            {
                Debug.Log($"[StrategicEnemyEntity] Combat strategy changed to: {combatStrategy?.GetType().Name ?? "null"}");
            }
        }

        public void SetMovementStrategy(MovementStrategy newStrategy)
        {
            if (newStrategy == movementStrategy)
                return;

            // Deactivate old strategy
            if (movementStrategy != null && movementStrategy.IsActive)
            {
                movementStrategy.Deactivate();
            }

            // Set new strategy
            movementStrategy = newStrategy;

            // Initialize and activate new strategy
            if (movementStrategy != null)
            {
                if (!movementStrategy.IsInitialized)
                {
                    movementStrategy.Initialize();
                }

                if (IsActive && !movementStrategy.IsActive)
                {
                    movementStrategy.Activate();
                    movementStrategy.SetTarget(currentTarget);
                }
            }

            OnMovementStrategyChanged?.Invoke(movementStrategy);

            if (enableDebugLogs)
            {
                Debug.Log($"[StrategicEnemyEntity] Movement strategy changed to: {movementStrategy?.GetType().Name ?? "null"}");
            }
        }

        private void CheckForStrategyChanges(float healthPercentage)
        {
            // Example: Change strategies based on health
            if (healthPercentage < 0.5f && availableCombatStrategies != null && availableCombatStrategies.Length > 1)
            {
                // Switch to more aggressive strategy when low on health
                int newIndex = (currentCombatStrategyIndex + 1) % availableCombatStrategies.Length;
                if (newIndex != currentCombatStrategyIndex)
                {
                    currentCombatStrategyIndex = newIndex;
                    SetCombatStrategy(availableCombatStrategies[currentCombatStrategyIndex]);
                }
            }
        }

        private void DeactivateAfterDeath()
        {
            if (IsActive)
            {
                Deactivate();
            }
        }

        // Public API
        public CombatStrategy CurrentCombatStrategy => combatStrategy;
        public MovementStrategy CurrentMovementStrategy => movementStrategy;
        public Transform CurrentTarget => currentTarget;

#if UNITY_EDITOR
        private void OnValidate()
        {
            // Prevent inspector serialization issues
            if (!Application.isPlaying)
            {
                // Clear runtime arrays in editor
                if (availableCombatStrategies != null && availableCombatStrategies.Length == 0)
                    availableCombatStrategies = null;
                if (availableMovementStrategies != null && availableMovementStrategies.Length == 0)
                    availableMovementStrategies = null;
            }
        }

        [ContextMenu("Cycle Combat Strategy")]
        private void CycleCombatStrategy()
        {
            if (availableCombatStrategies != null && availableCombatStrategies.Length > 1)
            {
                currentCombatStrategyIndex = (currentCombatStrategyIndex + 1) % availableCombatStrategies.Length;
                SetCombatStrategy(availableCombatStrategies[currentCombatStrategyIndex]);
            }
        }

        [ContextMenu("Cycle Movement Strategy")]
        private void CycleMovementStrategy()
        {
            if (availableMovementStrategies != null && availableMovementStrategies.Length > 1)
            {
                currentMovementStrategyIndex = (currentMovementStrategyIndex + 1) % availableMovementStrategies.Length;
                SetMovementStrategy(availableMovementStrategies[currentMovementStrategyIndex]);
            }
        }
#endif
    }
}
