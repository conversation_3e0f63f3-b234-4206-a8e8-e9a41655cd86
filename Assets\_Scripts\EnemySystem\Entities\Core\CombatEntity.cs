using UnityEngine;
using System;

namespace BTR.EnemySystem.Entities
{
    /// <summary>
    /// Base implementation of ICombatEntity providing combat functionality.
    /// Extends BaseEntity with health, damage, and combat systems.
    /// </summary>
    public abstract class CombatEntity : BaseEntity, ICombatEntity
    {
        [Header("Combat Settings")]
        [SerializeField] protected float maxHealth = 100f;
        [SerializeField] protected bool startVulnerable = true;
        [SerializeField] protected bool resetHealthOnActivation = true;

        [Header("Configuration")]
        [SerializeField] protected EnemyConfiguration configuration;

        // ICombatEntity Properties
        public float Health { get; private set; }
        public float MaxHealth => maxHealth;
        public bool IsVulnerable { get; private set; }
        public bool IsAlive => Health > 0f;

        // Configuration access
        public EnemyConfiguration Configuration => configuration;

        // ICombatEntity Events
        public event Action<float> OnDamageReceived;
        public event Action<float, float> OnHealthChanged;
        public event Action OnDeath;
        public event Action<bool> OnVulnerabilityChanged;

        protected override void PerformInitialization()
        {
            // Initialize health
            Health = maxHealth;
            IsVulnerable = startVulnerable;

            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Combat entity initialized - Health: {Health}/{MaxHealth}, Vulnerable: {IsVulnerable}");
            }

            // Call subclass initialization
            PerformCombatInitialization();
        }

        protected override void PerformActivation()
        {
            if (resetHealthOnActivation)
            {
                ResetHealth();
            }

            IsVulnerable = startVulnerable;

            // Call subclass activation
            PerformCombatActivation();
        }

        protected override void PerformDeactivation()
        {
            // Call subclass deactivation
            PerformCombatDeactivation();
        }

        protected override void PerformCleanup()
        {
            // Clear combat events
            OnDamageReceived = null;
            OnHealthChanged = null;
            OnDeath = null;
            OnVulnerabilityChanged = null;

            // Call subclass cleanup
            PerformCombatCleanup();
        }

        public virtual void TakeDamage(float amount)
        {
            if (!IsAlive || !IsVulnerable || amount <= 0f)
            {
                if (enableDebugLogs)
                {
                    Debug.Log($"[{GetType().Name}] Damage rejected - Alive: {IsAlive}, Vulnerable: {IsVulnerable}, Amount: {amount}");
                }
                return;
            }

            float oldHealth = Health;
            Health = Mathf.Max(0f, Health - amount);

            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Took {amount} damage - Health: {oldHealth} -> {Health}");
            }

            // Trigger events
            OnDamageReceived?.Invoke(amount);
            OnHealthChanged?.Invoke(oldHealth, Health);

            // Handle damage effects
            HandleDamageReceived(amount, oldHealth, Health);

            // Check for death
            if (Health <= 0f && oldHealth > 0f)
            {
                HandleDeath();
            }
        }

        public virtual void Heal(float amount)
        {
            if (!IsAlive || amount <= 0f)
            {
                return;
            }

            float oldHealth = Health;
            Health = Mathf.Min(MaxHealth, Health + amount);

            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Healed {amount} - Health: {oldHealth} -> {Health}");
            }

            // Trigger events
            OnHealthChanged?.Invoke(oldHealth, Health);

            // Handle healing effects
            HandleHealing(amount, oldHealth, Health);
        }

        public virtual void SetVulnerability(bool isVulnerable)
        {
            if (IsVulnerable == isVulnerable)
            {
                return;
            }

            IsVulnerable = isVulnerable;

            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Vulnerability changed to: {IsVulnerable}");
            }

            OnVulnerabilityChanged?.Invoke(IsVulnerable);
            HandleVulnerabilityChanged(IsVulnerable);
        }

        public virtual void SetMaxHealth(float newMaxHealth)
        {
            if (newMaxHealth <= 0f)
            {
                Debug.LogWarning($"[{GetType().Name}] Invalid max health value: {newMaxHealth}");
                return;
            }

            float oldMaxHealth = maxHealth;
            maxHealth = newMaxHealth;

            // Adjust current health proportionally
            if (oldMaxHealth > 0f)
            {
                float healthRatio = Health / oldMaxHealth;
                float oldHealth = Health;
                Health = Mathf.Min(newMaxHealth, newMaxHealth * healthRatio);

                if (enableDebugLogs)
                {
                    Debug.Log($"[{GetType().Name}] Max health changed: {oldMaxHealth} -> {newMaxHealth}, Current health: {oldHealth} -> {Health}");
                }

                OnHealthChanged?.Invoke(oldHealth, Health);
            }
        }

        public virtual void ResetHealth()
        {
            float oldHealth = Health;
            Health = MaxHealth;

            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Health reset: {oldHealth} -> {Health}");
            }

            OnHealthChanged?.Invoke(oldHealth, Health);
        }

        protected virtual void HandleDeath()
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[{GetType().Name}] Entity died");
            }

            OnDeath?.Invoke();
            PerformDeathHandling();
        }

        // Abstract methods for subclasses to implement
        protected abstract void PerformCombatInitialization();
        protected abstract void PerformCombatActivation();
        protected abstract void PerformCombatDeactivation();
        protected abstract void PerformCombatCleanup();
        protected abstract void HandleDamageReceived(float damage, float oldHealth, float newHealth);
        protected abstract void HandleHealing(float healing, float oldHealth, float newHealth);
        protected abstract void HandleVulnerabilityChanged(bool isVulnerable);
        protected abstract void PerformDeathHandling();
    }
}
