using UnityEngine;
using System.Collections.Generic;
using BTR;


namespace BTR.EnemySystem.Strategies.Combat
{
    /// <summary>
    /// Combat strategy that fires projectiles at targets.
    /// Migrated from ProjectileCombatBehavior to the new strategy pattern.
    /// </summary>
    public class ProjectileCombatStrategy : CombatStrategy
    {
        [Header("Projectile Settings")]
        [SerializeField] private Transform[] projectileOrigins;
        [SerializeField] private float projectileSpeed = 10f;
        [SerializeField] private float projectileDamage = 10f;
        [SerializeField] private float projectileLifetime = 5f;
        [SerializeField] private float projectileScale = 1f;
        [SerializeField] private int projectilesPerAttack = 1;
        [SerializeField] private float spreadAngle = 0f;
        [SerializeField] private bool useHoming = false;

        [Header("Configuration")]
        [SerializeField] private EnemyConfiguration configuration;

        [Header("Audio Settings")]
        [SerializeField] private string attackSoundEvent = "enemy_projectile_fire";

        // Projectile tracking
        private List<GameObject> activeProjectiles = new List<GameObject>();
        private int currentOriginIndex = 0;

        protected override void PerformCombatInitialization()
        {
            // Apply configuration settings if available
            ApplyConfigurationSettings();

            // Validate ProjectileManager availability
            if (ProjectileManager.Instance == null)
            {
                Debug.LogError($"[ProjectileCombatStrategy] ProjectileManager.Instance is null! Cannot spawn projectiles on {gameObject.name}");
            }

            if (projectileOrigins == null || projectileOrigins.Length == 0)
            {
                // Create default origin at entity position
                projectileOrigins = new Transform[] { entityTransform };
                Debug.Log($"[ProjectileCombatStrategy] Using entity transform as default projectile origin on {gameObject.name}");
            }

            // Initialize projectile tracking
            activeProjectiles.Clear();
            currentOriginIndex = 0;

            Debug.Log($"[ProjectileCombatStrategy] Initialized on {gameObject.name} - Damage: {projectileDamage}, Speed: {projectileSpeed}, Lifetime: {projectileLifetime}s, Scale: {projectileScale}, Homing: {useHoming}");
        }

        protected override void PerformCombatActivation()
        {
            // Clear any existing projectiles
            CleanupProjectiles();

            Debug.Log($"[ProjectileCombatStrategy] Combat activated on {gameObject.name}");
        }

        protected override void PerformCombatDeactivation()
        {
            // Clean up projectiles when deactivating
            CleanupProjectiles();

            if (enableDebugLogs)
            {
                Debug.Log($"[ProjectileCombatStrategy] Combat deactivated");
            }
        }

        protected override void PerformCombatCleanup()
        {
            // Clean up all projectiles
            CleanupProjectiles();

            // Update migration status


            if (enableDebugLogs)
            {
                Debug.Log($"[ProjectileCombatStrategy] Combat cleaned up");
            }
        }

        protected override void PerformCombatUpdate()
        {
            // Clean up destroyed projectiles
            CleanupDestroyedProjectiles();
        }

        protected override bool ShouldAttack()
        {
            // Attack if we have a target and ProjectileManager is available
            bool shouldAttack = currentTarget != null && ProjectileManager.Instance != null;

            if (!shouldAttack)
            {
                Debug.Log($"[ProjectileCombatStrategy] ShouldAttack = false on {gameObject.name} - Target: {(currentTarget != null ? currentTarget.name : "NULL")}, ProjectileManager: {(ProjectileManager.Instance != null ? "Available" : "NULL")}");
            }
            else
            {
                Debug.Log($"[ProjectileCombatStrategy] ShouldAttack = true on {gameObject.name} - Target: {currentTarget.name}");
            }

            return shouldAttack;
        }

        protected override void ExecuteAttack()
        {
            if (ProjectileManager.Instance == null || currentTarget == null)
            {
                if (enableDebugLogs)
                {
                    Debug.LogWarning($"[ProjectileCombatStrategy] Cannot execute attack - missing ProjectileManager or target");
                }
                return;
            }

            // Fire projectiles
            for (int i = 0; i < projectilesPerAttack; i++)
            {
                FireProjectile();
            }

            // Play attack sound
            PlayAttackSound();

            if (enableDebugLogs)
            {
                Debug.Log($"[ProjectileCombatStrategy] Fired {projectilesPerAttack} projectiles at {currentTarget.name}");
            }
        }

        private void FireProjectile()
        {
            // Validate ProjectileManager
            if (ProjectileManager.Instance == null)
            {
                Debug.LogError($"[ProjectileCombatStrategy] ProjectileManager.Instance is null!");
                return;
            }

            // Get projectile origin
            Transform origin = GetNextProjectileOrigin();
            if (origin == null)
            {
                Debug.LogWarning($"[ProjectileCombatStrategy] No valid projectile origin found");
                return;
            }

            // Calculate direction to target
            Vector3 direction = (currentTarget.position - origin.position).normalized;
            Quaternion shootRotation = Quaternion.LookRotation(direction);

            // Apply spread if configured
            if (spreadAngle > 0f)
            {
                float randomAngle = Random.Range(-spreadAngle, spreadAngle);
                direction = Quaternion.AngleAxis(randomAngle, Vector3.up) * direction;
                shootRotation = Quaternion.LookRotation(direction);
            }

            // Spawn projectile using ProjectileManager (like the old system)
            var projectile = ProjectileManager.Instance.SpawnProjectile(
                origin.position,
                shootRotation,
                projectileSpeed,
                projectileLifetime,
                projectileScale,
                projectileDamage,
                useHoming,
                null, // material
                currentTarget,
                false // isPlayerProjectile
            );

            if (projectile != null)
            {
                // Track projectile GameObject for cleanup
                activeProjectiles.Add(projectile.gameObject);

                if (enableDebugLogs)
                {
                    Debug.Log($"[ProjectileCombatStrategy] Projectile spawned successfully - Speed: {projectileSpeed}, Damage: {projectileDamage}, Lifetime: {projectileLifetime}s");
                }
            }
            else
            {
                Debug.LogWarning($"[ProjectileCombatStrategy] Failed to spawn projectile from ProjectileManager");
            }
        }

        private Transform GetNextProjectileOrigin()
        {
            if (projectileOrigins == null || projectileOrigins.Length == 0)
                return entityTransform;

            Transform origin = projectileOrigins[currentOriginIndex];
            currentOriginIndex = (currentOriginIndex + 1) % projectileOrigins.Length;

            return origin != null ? origin : entityTransform;
        }



        private void CleanupProjectiles()
        {
            foreach (GameObject projectile in activeProjectiles)
            {
                if (projectile != null)
                {
                    Destroy(projectile);
                }
            }
            activeProjectiles.Clear();
        }

        private void CleanupDestroyedProjectiles()
        {
            activeProjectiles.RemoveAll(projectile => projectile == null);
        }

        private void PlayAttackSound()
        {
            if (string.IsNullOrEmpty(attackSoundEvent))
                return;

            // Try to play FMOD sound if available
            try
            {
                // This would integrate with your existing FMOD system
                // For now, just log the sound event
                if (enableDebugLogs)
                {
                    Debug.Log($"[ProjectileCombatStrategy] Playing sound: {attackSoundEvent}");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"[ProjectileCombatStrategy] Failed to play sound {attackSoundEvent}: {e.Message}");
            }
        }

        // Public API for configuration
        public void SetProjectileOrigins(Transform[] origins)
        {
            projectileOrigins = origins;
            currentOriginIndex = 0;
        }

        public void SetProjectileSpeed(float speed)
        {
            projectileSpeed = Mathf.Max(0f, speed);
        }

        public void SetProjectileDamage(float damage)
        {
            projectileDamage = Mathf.Max(0f, damage);
        }

        public void SetProjectilesPerAttack(int count)
        {
            projectilesPerAttack = Mathf.Max(1, count);
        }

        public void SetSpreadAngle(float angle)
        {
            spreadAngle = Mathf.Clamp(angle, 0f, 180f);
        }

        public void SetProjectileScale(float scale)
        {
            projectileScale = Mathf.Max(0.1f, scale);
        }

        public void SetUseHoming(bool homing)
        {
            useHoming = homing;
        }

        /// <summary>
        /// Apply settings from EnemyConfiguration if available
        /// </summary>
        private void ApplyConfigurationSettings()
        {
            if (configuration != null)
            {
                // Apply projectile settings from configuration
                projectileSpeed = configuration.projectileSpeed;
                projectileDamage = configuration.projectileDamage;
                projectileLifetime = configuration.projectileLifetime;
                projectileScale = configuration.projectileScale;
                useHoming = configuration.enableHoming;

                if (enableDebugLogs)
                {
                    Debug.Log($"[ProjectileCombatStrategy] Applied configuration settings - Speed: {projectileSpeed}, Damage: {projectileDamage}, Lifetime: {projectileLifetime}s, Scale: {projectileScale}, Homing: {useHoming}");
                }
            }
            else
            {
                // Try to find configuration from parent entity
                var entity = GetComponent<CombatEntity>();
                if (entity != null && entity.Configuration != null)
                {
                    configuration = entity.Configuration;
                    ApplyConfigurationSettings(); // Recursive call with found configuration
                    return;
                }

                if (enableDebugLogs)
                {
                    Debug.Log($"[ProjectileCombatStrategy] No configuration found, using serialized values - Homing: {useHoming}");
                }
            }
        }

        /// <summary>
        /// Set the configuration for this strategy
        /// </summary>
        public void SetConfiguration(EnemyConfiguration config)
        {
            configuration = config;
            ApplyConfigurationSettings();
        }

        // Debug information
        public int ActiveProjectileCount => activeProjectiles.Count;
        public bool IsHomingEnabled => useHoming;
        public EnemyConfiguration CurrentConfiguration => configuration;

#if UNITY_EDITOR
        [ContextMenu("Test Fire Projectile")]
        private void TestFireProjectile()
        {
            if (Application.isPlaying && currentTarget != null)
            {
                FireProjectile();
            }
        }

        [ContextMenu("Debug Configuration Status")]
        private void DebugConfigurationStatus()
        {
            Debug.Log($"[ProjectileCombatStrategy] Configuration Status on {gameObject.name}:\n" +
                     $"- Configuration: {(configuration != null ? configuration.name : "NULL")}\n" +
                     $"- Homing Enabled: {useHoming}\n" +
                     $"- Config Homing: {(configuration != null ? configuration.enableHoming.ToString() : "N/A")}\n" +
                     $"- Speed: {projectileSpeed}\n" +
                     $"- Damage: {projectileDamage}");
        }
#endif
    }

    // Interface for damage dealing (would be defined elsewhere in your system)
    public interface IDamageDealer
    {
        void SetDamage(float damage);
    }
}
