# Projectile Homing Configuration Test Guide

## Overview
This guide helps you verify that the enemy projectile homing configuration fix is working correctly.

## What Was Fixed
- `ProjectileCombatStrategy` now reads homing settings from `EnemyConfiguration`
- Enemy projectiles should now be homing by default (matching legacy behavior)
- Homing projectiles should appear correctly on the radar system

## Testing Steps

### 1. Check Configuration in Inspector
1. Find an enemy prefab that uses `StrategicEnemyEntity` + `ProjectileCombatStrategy`
2. In the Inspector, verify:
   - `StrategicEnemyEntity` has an `EnemyConfiguration` assigned
   - The `EnemyConfiguration` has `Enable Homing` checked (should be true by default)
   - `ProjectileCombatStrategy` may have its own configuration field (optional)

### 2. Runtime Debug Check
1. Start the game and find an enemy with `ProjectileCombatStrategy`
2. Right-click the enemy in the Hierarchy
3. Select "Debug Configuration Status" from the context menu
4. Check the console log for:
   ```
   [ProjectileCombatStrategy] Configuration Status on [EnemyName]:
   - Configuration: [ConfigName] (should not be NULL)
   - Homing Enabled: True (should be True)
   - Config Homing: True (should be True)
   - Speed: [value]
   - Damage: [value]
   ```

### 3. Verify Projectile Behavior
1. Let the enemy shoot projectiles at the player
2. Observe that projectiles:
   - Curve toward the player (homing behavior)
   - Appear on the radar as homing projectiles (different visual indicator)
   - Track the player's movement

### 4. Check Radar Integration
1. Open the radar display
2. When homing projectiles are fired, they should:
   - Appear as distinct icons (different from non-homing)
   - Update their position as they track the player
   - Disappear when they hit or expire

## Expected Results

### Before Fix
- Enemy projectiles flew straight (no homing)
- Projectiles didn't appear on radar as expected
- `useHoming` was false by default in `ProjectileCombatStrategy`

### After Fix
- Enemy projectiles home toward the player
- Projectiles appear correctly on radar
- Configuration is automatically applied from `EnemyConfiguration`

## Troubleshooting

### If Homing Still Not Working
1. **Check Configuration Assignment**:
   - Ensure `StrategicEnemyEntity.configuration` is assigned
   - Verify `EnemyConfiguration.enableHoming` is true

2. **Check Strategy Type**:
   - Ensure enemy is using `ProjectileCombatStrategy` (not legacy `ProjectileCombatBehavior`)
   - Verify the strategy is being initialized properly

3. **Check Debug Logs**:
   - Look for initialization logs showing homing status
   - Enable debug logs on the strategy if needed

### If Configuration Not Applied
1. **Manual Configuration**:
   - Assign configuration directly to `ProjectileCombatStrategy.configuration`
   - Call "Debug Configuration Status" to verify

2. **Legacy Fallback**:
   - If using legacy system, check `ProjectileCombatBehavior.enableHoming`
   - Ensure proper migration to new system

## Migration Notes

### For Existing Prefabs
- Existing enemy prefabs using `ProjectileCombatStrategy` should automatically pick up homing behavior if they have `EnemyConfiguration` assigned
- No manual changes needed for most cases

### For New Prefabs
- Assign an `EnemyConfiguration` to the `StrategicEnemyEntity`
- Ensure the configuration has `enableHoming = true`
- The strategy will automatically inherit these settings

## Related Files Modified
- `ProjectileCombatStrategy.cs` - Added configuration support
- `CombatEntity.cs` - Added configuration property
- `StrategicEnemyEntity.cs` - Added strategy configuration logic
- `ProjectileTrackingManager.cs` - Fixed homing detection (separate issue)
